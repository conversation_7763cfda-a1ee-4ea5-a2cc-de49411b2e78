import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:dasso_reader/config/platform_adaptations.dart';
import 'package:dasso_reader/utils/log/common.dart';

/// iOS Keyboard Focus Fix Utility
///
/// Provides comprehensive solutions for iOS keyboard focus issues while
/// maintaining perfect Android compatibility. This utility addresses common
/// iOS-specific problems where keyboards don't appear when tapping TextFields.
///
/// Key Features:
/// - iOS-only fixes (zero impact on Android)
/// - Support for both TextField and TextFormField
/// - Support for single-line and multiline inputs
/// - Proper Chinese input support
/// - Clean, reusable API
/// - Type-safe implementation
class IOSKeyboardFix {
  static const String _tag = 'IOSKeyboardFix';

  // Private constructor to prevent instantiation
  IOSKeyboardFix._();

  // =====================================================
  // CORE FOCUS METHODS
  // =====================================================

  /// Requests focus for a FocusNode with iOS-specific timing fixes
  ///
  /// This method handles the iOS keyboard timing issues by using
  /// multiple strategies to ensure reliable keyboard appearance.
  ///
  /// Usage:
  /// ```dart
  /// IOSKeyboardFix.requestFocus(myFocusNode);
  /// ```
  static void requestFocus(FocusNode focusNode) {
    if (!PlatformAdaptations.isIOS) {
      // On Android, use direct focus request (already works fine)
      focusNode.requestFocus();
      return;
    }

    // iOS-specific focus handling with multiple timing strategies
    if (focusNode.canRequestFocus) {
      try {
        // Strategy 1: Immediate focus request
        focusNode.requestFocus();

        // Strategy 2: Post-frame callback for complex layouts
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (focusNode.canRequestFocus && !focusNode.hasFocus) {
            focusNode.requestFocus();
          }
        });

        // Strategy 3: Small delay for stubborn cases
        Future.delayed(const Duration(milliseconds: 50), () {
          if (focusNode.canRequestFocus && !focusNode.hasFocus) {
            focusNode.requestFocus();
          }
        });

        AnxLog.info('$_tag: iOS focus strategies applied');
      } catch (e) {
        AnxLog.warning('$_tag: iOS focus request failed: $e');
      }
    }
  }

  /// Requests focus with additional iOS-specific delay for complex layouts
  ///
  /// Use this method when dealing with complex widget hierarchies or
  /// when the standard requestFocus doesn't work reliably.
  ///
  /// Usage:
  /// ```dart
  /// IOSKeyboardFix.requestFocusWithDelay(myFocusNode);
  /// ```
  static void requestFocusWithDelay(
    FocusNode focusNode, {
    Duration delay = const Duration(milliseconds: 100),
  }) {
    if (!PlatformAdaptations.isIOS) {
      // On Android, use direct focus request
      focusNode.requestFocus();
      return;
    }

    // iOS-specific delayed focus handling
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(delay, () {
        if (focusNode.canRequestFocus) {
          try {
            focusNode.requestFocus();
            AnxLog.info('$_tag: iOS delayed focus requested successfully');
          } catch (e) {
            AnxLog.warning('$_tag: iOS delayed focus request failed: $e');
          }
        }
      });
    });
  }

  // =====================================================
  // WIDGET WRAPPERS
  // =====================================================

  /// Creates an iOS-compatible TextField with proper focus handling
  ///
  /// This wrapper ensures that the TextField works correctly on iOS
  /// while maintaining perfect Android compatibility.
  ///
  /// Usage:
  /// ```dart
  /// IOSKeyboardFix.createTextField(
  ///   controller: myController,
  ///   focusNode: myFocusNode,
  ///   decoration: InputDecoration(hintText: 'Enter text'),
  /// )
  /// ```
  static Widget createTextField({
    TextEditingController? controller,
    FocusNode? focusNode,
    InputDecoration? decoration,
    TextStyle? style,
    TextInputType? keyboardType,
    TextCapitalization textCapitalization = TextCapitalization.none,
    int? maxLines = 1,
    int? minLines,
    bool expands = false,
    bool enabled = true,
    bool readOnly = false,
    String? semanticLabel,
    ValueChanged<String>? onChanged,
    VoidCallback? onTap,
    ValueChanged<String>? onSubmitted,
  }) {
    final effectiveFocusNode = focusNode ?? FocusNode();

    Widget textField = TextField(
      controller: controller,
      focusNode: effectiveFocusNode,
      decoration: decoration,
      style: style,
      keyboardType: keyboardType ?? _getOptimalKeyboardType(maxLines),
      textCapitalization: textCapitalization,
      maxLines: maxLines,
      minLines: minLines,
      expands: expands,
      enabled: enabled,
      readOnly: readOnly,
      onChanged: onChanged,
      onSubmitted: onSubmitted,
      onTap: () {
        // Handle custom onTap first
        onTap?.call();

        // Apply iOS-specific focus fix
        if (PlatformAdaptations.isIOS && enabled && !readOnly) {
          requestFocus(effectiveFocusNode);
        }
      },
    );

    // Wrap with GestureDetector for iOS touch handling
    if (PlatformAdaptations.isIOS) {
      textField = GestureDetector(
        onTap: () {
          if (enabled && !readOnly) {
            requestFocus(effectiveFocusNode);
          }
        },
        child: textField,
      );
    }

    // Add semantic label if provided
    if (semanticLabel != null) {
      textField = Semantics(
        label: semanticLabel,
        textField: true,
        child: textField,
      );
    }

    return textField;
  }

  /// Creates an iOS-compatible TextFormField with proper focus handling
  ///
  /// This wrapper ensures that the TextFormField works correctly on iOS
  /// while maintaining perfect Android compatibility.
  ///
  /// Usage:
  /// ```dart
  /// IOSKeyboardFix.createTextFormField(
  ///   controller: myController,
  ///   focusNode: myFocusNode,
  ///   decoration: InputDecoration(hintText: 'Enter text'),
  ///   validator: (value) => value?.isEmpty == true ? 'Required' : null,
  /// )
  /// ```
  static Widget createTextFormField({
    TextEditingController? controller,
    FocusNode? focusNode,
    InputDecoration? decoration,
    TextStyle? style,
    TextInputType? keyboardType,
    TextCapitalization textCapitalization = TextCapitalization.none,
    int? maxLines = 1,
    int? minLines,
    bool expands = false,
    bool enabled = true,
    bool readOnly = false,
    String? initialValue,
    String? semanticLabel,
    FormFieldValidator<String>? validator,
    ValueChanged<String>? onChanged,
    VoidCallback? onTap,
    ValueChanged<String>? onFieldSubmitted,
    FormFieldSetter<String>? onSaved,
  }) {
    final effectiveFocusNode = focusNode ?? FocusNode();

    Widget textFormField = TextFormField(
      controller: controller,
      focusNode: effectiveFocusNode,
      decoration: decoration,
      style: style,
      keyboardType: keyboardType ?? _getOptimalKeyboardType(maxLines),
      textCapitalization: textCapitalization,
      maxLines: maxLines,
      minLines: minLines,
      expands: expands,
      enabled: enabled,
      readOnly: readOnly,
      initialValue: initialValue,
      validator: validator,
      onChanged: onChanged,
      onFieldSubmitted: onFieldSubmitted,
      onSaved: onSaved,
      onTap: () {
        // Handle custom onTap first
        onTap?.call();

        // Apply iOS-specific focus fix
        if (PlatformAdaptations.isIOS && enabled && !readOnly) {
          requestFocus(effectiveFocusNode);
        }
      },
    );

    // Wrap with GestureDetector for iOS touch handling
    if (PlatformAdaptations.isIOS) {
      textFormField = GestureDetector(
        onTap: () {
          if (enabled && !readOnly) {
            requestFocus(effectiveFocusNode);
          }
        },
        child: textFormField,
      );
    }

    // Add semantic label if provided
    if (semanticLabel != null) {
      textFormField = Semantics(
        label: semanticLabel,
        textField: true,
        child: textFormField,
      );
    }

    return textFormField;
  }

  // =====================================================
  // UTILITY METHODS
  // =====================================================

  /// Returns optimal keyboard type based on field configuration
  static TextInputType _getOptimalKeyboardType(int? maxLines) {
    if (maxLines == null || maxLines > 1) {
      return TextInputType.multiline;
    }
    return TextInputType.text;
  }

  /// Checks if a FocusNode can safely request focus
  static bool canRequestFocus(FocusNode focusNode) {
    return focusNode.canRequestFocus && focusNode.context != null;
  }

  /// Unfocuses all text fields (useful for dismissing keyboard)
  static void unfocusAll(BuildContext context) {
    FocusScope.of(context).unfocus();
  }

  /// Checks if keyboard is currently visible (iOS-specific)
  static bool isKeyboardVisible(BuildContext context) {
    if (!PlatformAdaptations.isIOS) {
      return MediaQuery.of(context).viewInsets.bottom > 0;
    }

    // iOS-specific keyboard detection
    final viewInsets = MediaQuery.of(context).viewInsets;
    return viewInsets.bottom > 100; // iOS keyboard threshold
  }
}
